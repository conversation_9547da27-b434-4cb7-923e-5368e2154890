import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Divider,
  Chip,
  Grid
} from '@mui/material';
import { Store, Login as LoginIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const Login = () => {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login, mockUsers } = useAuth();

  const handleChange = (e) => {
    setCredentials({
      ...credentials,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(credentials.username, credentials.password);
    
    if (!result.success) {
      setError(result.error);
    }
    
    setLoading(false);
  };

  const handleDemoLogin = (username, password) => {
    setCredentials({ username, password });
    setError('');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2
      }}
    >
      <Card sx={{ maxWidth: 500, width: '100%', borderRadius: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Store sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h4" component="h1" gutterBottom>
              Maboutique
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Système de Gestion de Boutique
            </Typography>
          </Box>

          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Nom d'utilisateur"
              name="username"
              value={credentials.username}
              onChange={handleChange}
              margin="normal"
              required
              autoFocus
            />
            <TextField
              fullWidth
              label="Mot de passe"
              name="password"
              type="password"
              value={credentials.password}
              onChange={handleChange}
              margin="normal"
              required
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <LoginIcon />}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {loading ? 'Connexion...' : 'Se connecter'}
            </Button>
          </form>

          <Divider sx={{ my: 3 }}>
            <Chip label="Comptes de démonstration" size="small" />
          </Divider>

          <Typography variant="subtitle2" gutterBottom color="text.secondary">
            Cliquez sur un rôle pour vous connecter automatiquement :
          </Typography>

          <Grid container spacing={1} sx={{ mt: 1 }}>
            {mockUsers.map((user) => (
              <Grid item xs={12} key={user.id}>
                <Button
                  fullWidth
                  variant="outlined"
                  size="small"
                  onClick={() => handleDemoLogin(
                    user.username, 
                    user.username === 'admin' ? 'admin123' : 
                    user.username === 'superadmin' ? 'super123' : 'emp123'
                  )}
                  sx={{ 
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1
                  }}
                >
                  <Box sx={{ textAlign: 'left' }}>
                    <Typography variant="body2" fontWeight="medium">
                      {user.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Rôle: {user.role === 'superadmin' ? 'Super Admin' : 
                             user.role === 'admin' ? 'Administrateur' : 'Employé'}
                    </Typography>
                  </Box>
                </Button>
              </Grid>
            ))}
          </Grid>

          <Typography variant="caption" color="text.secondary" sx={{ mt: 3, display: 'block', textAlign: 'center' }}>
            Version de démonstration - Données simulées
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Login;
