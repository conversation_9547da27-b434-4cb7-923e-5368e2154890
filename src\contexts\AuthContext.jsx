import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock users for demonstration
const MOCK_USERS = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    name: 'Administrateur Principal',
    email: '<EMAIL>'
  },
  {
    id: 2,
    username: 'superadmin',
    password: 'super123',
    role: 'superadmin',
    name: 'Super Administrateur',
    email: '<EMAIL>'
  },
  {
    id: 3,
    username: 'employe',
    password: 'emp123',
    role: 'employee',
    name: 'Employ<PERSON> Boutique',
    email: '<EMAIL>'
  }
];

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for stored authentication with timeout
    console.log('AuthContext: Checking stored authentication...');

    const checkAuth = async () => {
      try {
        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Auth check timeout')), 2000)
        );

        const authPromise = new Promise((resolve) => {
          try {
            const storedUser = localStorage.getItem('maboutique_user');
            if (storedUser) {
              console.log('AuthContext: Found stored user data');
              try {
                const userData = JSON.parse(storedUser);
                setUser(userData);
                setIsAuthenticated(true);
                console.log('AuthContext: User authenticated from storage');
              } catch (error) {
                console.error('AuthContext: Error parsing stored user data:', error);
                localStorage.removeItem('maboutique_user');
              }
            } else {
              console.log('AuthContext: No stored user data found');
            }
            resolve();
          } catch (error) {
            console.error('AuthContext: Error accessing localStorage:', error);
            resolve();
          }
        });

        await Promise.race([authPromise, timeoutPromise]);
      } catch (error) {
        console.error('AuthContext: Auth check failed:', error);
      } finally {
        setLoading(false);
        console.log('AuthContext: Authentication check completed');
      }
    };

    // Add small delay to ensure proper initialization
    const timer = setTimeout(checkAuth, 100);

    // Fallback timeout
    const fallbackTimer = setTimeout(() => {
      console.warn('AuthContext: Fallback timeout reached, forcing loading to false');
      setLoading(false);
    }, 3000);

    return () => {
      clearTimeout(timer);
      clearTimeout(fallbackTimer);
    };
  }, []);

  const login = async (username, password) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const foundUser = MOCK_USERS.find(
        u => u.username === username && u.password === password
      );

      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser;
        setUser(userWithoutPassword);
        setIsAuthenticated(true);
        localStorage.setItem('maboutique_user', JSON.stringify(userWithoutPassword));
        return { success: true };
      } else {
        return { 
          success: false, 
          error: 'Nom d\'utilisateur ou mot de passe incorrect' 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: 'Erreur de connexion. Veuillez réessayer.' 
      };
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('maboutique_user');
  };

  const hasPermission = (permission) => {
    if (!user) return false;

    const permissions = {
      superadmin: [
        'view_dashboard',
        'view_analytics_section',
        'view_revenue_dashboard',
        'manage_products',
        'add_products',
        'delete_products',
        'manage_sales',
        'view_reports',
        'view_analytics',
        'manage_users',
        'modify_financial_records',
        'system_settings'
      ],
      admin: [
        'view_dashboard',
        'view_products',
        'manage_sales',
        'view_analytics',
        'manage_users'
      ],
      employee: [
        'view_dashboard',
        'view_products',
        'manage_sales'
      ]
    };

    return permissions[user.role]?.includes(permission) || false;
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    hasPermission,
    mockUsers: MOCK_USERS.map(({ password, ...user }) => user) // For demo purposes
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
