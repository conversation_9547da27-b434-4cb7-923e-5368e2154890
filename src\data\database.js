// Database operations for both browser and Electron environments
// Uses localStorage for browser and can be extended for SQLite in Electron

import { isElectron, isBrowser, storage, logEnvironmentInfo } from '../utils/environment.js';

let isInitialized = false;
let databaseType = 'localStorage'; // Default to localStorage

export const initializeDatabase = async () => {
  if (isInitialized) return;

  try {
    // Log environment info for debugging
    logEnvironmentInfo();

    console.log('Initializing database...');

    // Determine database type based on environment
    if (isElectron()) {
      databaseType = 'sqlite'; // Could be extended to use SQLite in Electron
      console.log('Running in Electron environment - using localStorage for now');
    } else if (isBrowser()) {
      databaseType = 'localStorage';
      console.log('Running in browser environment - using localStorage');
    }

    // Check if we have existing data in storage
    const existingProducts = storage.get('maboutique_products');
    const existingSales = storage.get('maboutique_sales');
    const existingCategories = storage.get('maboutique_categories');

    if (!existingProducts || !existingSales || !existingCategories) {
      console.log('No existing data found, will use mock data');
    } else {
      console.log('Found existing data in storage');
    }

    isInitialized = true;
    console.log('Database initialized successfully with type:', databaseType);
  } catch (error) {
    console.error('Error initializing database:', error);
    // Don't throw the error, just log it and continue
    isInitialized = true; // Mark as initialized to prevent infinite retries
  }
};

// Product operations
export const saveProducts = (products) => {
  try {
    const success = storage.set('maboutique_products', products);
    return { success };
  } catch (error) {
    console.error('Error saving products:', error);
    return { success: false, error: error.message };
  }
};

export const loadProducts = () => {
  try {
    return storage.get('maboutique_products', []);
  } catch (error) {
    console.error('Error loading products:', error);
    return [];
  }
};

// Sales operations
export const saveSales = (sales) => {
  try {
    const success = storage.set('maboutique_sales', sales);
    return { success };
  } catch (error) {
    console.error('Error saving sales:', error);
    return { success: false, error: error.message };
  }
};

export const loadSales = () => {
  try {
    return storage.get('maboutique_sales', []);
  } catch (error) {
    console.error('Error loading sales:', error);
    return [];
  }
};

// Categories operations
export const saveCategories = (categories) => {
  try {
    const success = storage.set('maboutique_categories', categories);
    return { success };
  } catch (error) {
    console.error('Error saving categories:', error);
    return { success: false, error: error.message };
  }
};

export const loadCategories = () => {
  try {
    return storage.get('maboutique_categories', []);
  } catch (error) {
    console.error('Error loading categories:', error);
    return [];
  }
};

// Backup and restore operations
export const exportData = () => {
  try {
    const data = {
      products: loadProducts(),
      sales: loadSales(),
      categories: loadCategories(),
      exportDate: new Date().toISOString(),
      version: '1.0.0'
    };
    return { success: true, data };
  } catch (error) {
    console.error('Error exporting data:', error);
    return { success: false, error: error.message };
  }
};

export const importData = (data) => {
  try {
    if (data.products) {
      saveProducts(data.products);
    }
    if (data.sales) {
      saveSales(data.sales);
    }
    if (data.categories) {
      saveCategories(data.categories);
    }
    return { success: true };
  } catch (error) {
    console.error('Error importing data:', error);
    return { success: false, error: error.message };
  }
};

// Clear all data (for reset functionality)
export const clearAllData = () => {
  try {
    localStorage.removeItem('maboutique_products');
    localStorage.removeItem('maboutique_sales');
    localStorage.removeItem('maboutique_categories');
    return { success: true };
  } catch (error) {
    console.error('Error clearing data:', error);
    return { success: false, error: error.message };
  }
};
