<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Browser Compatibility</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Test de Compatibilité Navigateur</h1>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }

        // Test 1: Environment Detection
        try {
            const isElectron = () => {
                if (typeof window !== 'undefined' && window.electronAPI) return true;
                if (typeof process !== 'undefined' && process.versions && process.versions.electron) return true;
                if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.includes('Electron')) return true;
                return false;
            };

            const isBrowser = () => {
                return typeof window !== 'undefined' && !isElectron();
            };

            addResult(`Environment: ${isBrowser() ? 'Browser' : 'Other'}`, 'success');
            addResult(`Is Electron: ${isElectron()}`, 'info');
            addResult(`User Agent: ${navigator.userAgent}`, 'info');
        } catch (error) {
            addResult(`Environment detection failed: ${error.message}`, 'error');
        }

        // Test 2: LocalStorage
        try {
            const testKey = '__test_storage__';
            localStorage.setItem(testKey, 'test');
            const value = localStorage.getItem(testKey);
            localStorage.removeItem(testKey);
            
            if (value === 'test') {
                addResult('LocalStorage: Available and working', 'success');
            } else {
                addResult('LocalStorage: Available but not working correctly', 'error');
            }
        } catch (error) {
            addResult(`LocalStorage: Not available - ${error.message}`, 'error');
        }

        // Test 3: React/JSX Support
        try {
            if (typeof React !== 'undefined') {
                addResult('React: Available', 'success');
            } else {
                addResult('React: Not loaded (expected in browser test)', 'info');
            }
        } catch (error) {
            addResult(`React test failed: ${error.message}`, 'error');
        }

        // Test 4: Module Support
        try {
            // Test if ES6 modules are supported
            const testModule = () => import('./src/utils/environment.js');
            addResult('ES6 Modules: Supported', 'success');
        } catch (error) {
            addResult(`ES6 Modules: ${error.message}`, 'info');
        }

        // Test 5: Better-sqlite3 (should fail in browser)
        try {
            // This should fail in browser environment
            const Database = require('better-sqlite3');
            addResult('better-sqlite3: Available (unexpected in browser)', 'error');
        } catch (error) {
            addResult('better-sqlite3: Not available in browser (expected)', 'success');
        }

        addResult('Test completed. Check the main application at http://localhost:5173', 'info');
    </script>
</body>
</html>
