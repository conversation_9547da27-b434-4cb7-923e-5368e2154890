import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Grid,
  Box,
  Typography,
  Alert,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { AttachMoney, QrCode } from '@mui/icons-material';
import { useData } from '../../contexts/DataContext';

const ProductDialog = ({ open, product, onClose }) => {
  const { addProduct, updateProduct } = useData();
  const isEdit = Boolean(product);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    priceCDF: '',
    sku: '',
    categoryId: '',
    stock: '',
    minStock: '',
    barcode: '',
    image: ''
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const categories = [
    { id: 1, name: '<PERSON><PERSON>ronique' },
    { id: 2, name: 'Vêtements' },
    { id: 3, name: 'Alimentation' },
    { id: 4, name: 'Cosmétiques' },
    { id: 5, name: '<PERSON>son' },
    { id: 6, name: 'Sport' }
  ];

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name || '',
        description: product.description || '',
        price: product.price?.toString() || '',
        priceCDF: product.priceCDF?.toString() || '',
        sku: product.sku || '',
        categoryId: product.categoryId?.toString() || '',
        stock: product.stock?.toString() || '',
        minStock: product.minStock?.toString() || '',
        barcode: product.barcode || '',
        image: product.image || ''
      });
    } else {
      setFormData({
        name: '',
        description: '',
        price: '',
        priceCDF: '',
        sku: '',
        categoryId: '',
        stock: '',
        minStock: '',
        barcode: '',
        image: ''
      });
    }
    setErrors({});
  }, [product, open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Auto-calculate CDF price when USD price changes
    if (name === 'price' && value) {
      const usdPrice = parseFloat(value);
      if (!isNaN(usdPrice)) {
        const cdfPrice = Math.round(usdPrice * 2500); // 1 USD = 2500 CDF (example rate)
        setFormData(prev => ({
          ...prev,
          priceCDF: cdfPrice.toString()
        }));
      }
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const generateSKU = () => {
    const category = categories.find(c => c.id.toString() === formData.categoryId);
    const categoryCode = category ? category.name.substring(0, 3).toUpperCase() : 'GEN';
    const randomCode = Math.random().toString(36).substring(2, 8).toUpperCase();
    const sku = `${categoryCode}-${randomCode}`;
    
    setFormData(prev => ({
      ...prev,
      sku
    }));
  };

  const generateBarcode = () => {
    const barcode = Math.floor(Math.random() * 9000000000000) + 1000000000000;
    setFormData(prev => ({
      ...prev,
      barcode: barcode.toString()
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Le nom du produit est requis';
    }

    if (!formData.price || isNaN(parseFloat(formData.price)) || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Le prix USD doit être un nombre positif';
    }

    if (!formData.priceCDF || isNaN(parseFloat(formData.priceCDF)) || parseFloat(formData.priceCDF) <= 0) {
      newErrors.priceCDF = 'Le prix CDF doit être un nombre positif';
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'Le SKU est requis';
    }

    if (!formData.categoryId) {
      newErrors.categoryId = 'La catégorie est requise';
    }

    if (!formData.stock || isNaN(parseInt(formData.stock)) || parseInt(formData.stock) < 0) {
      newErrors.stock = 'Le stock doit être un nombre positif ou zéro';
    }

    if (!formData.minStock || isNaN(parseInt(formData.minStock)) || parseInt(formData.minStock) < 0) {
      newErrors.minStock = 'Le stock minimum doit être un nombre positif ou zéro';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      const productData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: parseFloat(formData.price),
        priceCDF: parseFloat(formData.priceCDF),
        sku: formData.sku.trim(),
        categoryId: parseInt(formData.categoryId),
        stock: parseInt(formData.stock),
        minStock: parseInt(formData.minStock),
        barcode: formData.barcode.trim(),
        image: formData.image.trim() || '/images/products/default.jpg'
      };

      if (isEdit) {
        updateProduct(product.id, productData);
      } else {
        addProduct(productData);
      }

      onClose();
    } catch (error) {
      console.error('Error saving product:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEdit ? 'Modifier le Produit' : 'Nouveau Produit'}
      </DialogTitle>
      
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Informations de Base
              </Typography>
            </Grid>

            <Grid item xs={12} md={8}>
              <TextField
                fullWidth
                label="Nom du Produit"
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!errors.name}
                helperText={errors.name}
                required
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth error={!!errors.categoryId} required>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  name="categoryId"
                  value={formData.categoryId}
                  onChange={handleChange}
                  label="Catégorie"
                >
                  {categories.map((category) => (
                    <MenuItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                multiline
                rows={3}
              />
            </Grid>

            {/* Pricing */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Prix
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Prix (USD)"
                name="price"
                type="number"
                value={formData.price}
                onChange={handleChange}
                error={!!errors.price}
                helperText={errors.price}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <AttachMoney />
                    </InputAdornment>
                  ),
                }}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Prix (CDF)"
                name="priceCDF"
                type="number"
                value={formData.priceCDF}
                onChange={handleChange}
                error={!!errors.priceCDF}
                helperText={errors.priceCDF || 'Calculé automatiquement à partir du prix USD'}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">FC</InputAdornment>
                  ),
                }}
                required
              />
            </Grid>

            {/* Inventory */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Inventaire
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Stock Actuel"
                name="stock"
                type="number"
                value={formData.stock}
                onChange={handleChange}
                error={!!errors.stock}
                helperText={errors.stock}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Stock Minimum"
                name="minStock"
                type="number"
                value={formData.minStock}
                onChange={handleChange}
                error={!!errors.minStock}
                helperText={errors.minStock || 'Seuil d\'alerte de stock faible'}
                required
              />
            </Grid>

            {/* Identification */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Identification
              </Typography>
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="SKU"
                name="sku"
                value={formData.sku}
                onChange={handleChange}
                error={!!errors.sku}
                helperText={errors.sku}
                InputProps={{
                  endAdornment: (
                    <Button size="small" onClick={generateSKU}>
                      Générer
                    </Button>
                  ),
                }}
                required
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Code-barres"
                name="barcode"
                value={formData.barcode}
                onChange={handleChange}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <QrCode />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <Button size="small" onClick={generateBarcode}>
                      Générer
                    </Button>
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="URL de l'Image"
                name="image"
                value={formData.image}
                onChange={handleChange}
                placeholder="/images/products/mon-produit.jpg"
                helperText="URL de l'image du produit (optionnel)"
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>
            Annuler
          </Button>
          <Button 
            type="submit" 
            variant="contained"
            disabled={loading}
          >
            {loading ? 'Enregistrement...' : (isEdit ? 'Modifier' : 'Créer')}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ProductDialog;
