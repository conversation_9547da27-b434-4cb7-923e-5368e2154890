// Mock data generator for demonstration purposes

export const generateMockData = () => {
  const categories = [
    { id: 1, name: 'Électronique', description: 'Appareils électroniques et accessoires' },
    { id: 2, name: 'Vêtements', description: 'Vêtements pour hommes, femmes et enfants' },
    { id: 3, name: '<PERSON><PERSON>', description: 'Produits alimentaires et boissons' },
    { id: 4, name: 'Cosmétiques', description: 'Produits de beauté et soins personnels' },
    { id: 5, name: '<PERSON><PERSON>', description: 'Articles pour la maison et décoration' },
    { id: 6, name: 'Sport', description: 'Équipements et vêtements de sport' }
  ];

  const products = [
    // Électronique
    {
      id: 1,
      name: 'Smartphone Samsung Galaxy A54',
      description: 'Smartphone Android avec écran 6.4" et appareil photo 50MP',
      price: 350,
      priceCDF: 875000,
      sku: 'ELEC-SAM-A54',
      categoryId: 1,
      stock: 15,
      minStock: 5,
      image: '/images/products/samsung-a54.jpg',
      barcode: '8801643670123',
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z'
    },
    {
      id: 2,
      name: 'Écouteurs Bluetooth JBL',
      description: 'Écouteurs sans fil avec réduction de bruit active',
      price: 80,
      priceCDF: 200000,
      sku: 'ELEC-JBL-BT01',
      categoryId: 1,
      stock: 25,
      minStock: 10,
      image: '/images/products/jbl-earbuds.jpg',
      barcode: '6925281954123',
      createdAt: '2024-01-10T14:30:00Z',
      updatedAt: '2024-01-10T14:30:00Z'
    },
    {
      id: 3,
      name: 'Chargeur USB-C Rapide',
      description: 'Chargeur rapide 25W compatible avec la plupart des appareils',
      price: 25,
      priceCDF: 62500,
      sku: 'ELEC-CHG-USBC',
      categoryId: 1,
      stock: 50,
      minStock: 20,
      image: '/images/products/usb-c-charger.jpg',
      barcode: '1234567890123',
      createdAt: '2024-01-08T09:15:00Z',
      updatedAt: '2024-01-08T09:15:00Z'
    },

    // Vêtements
    {
      id: 4,
      name: 'T-shirt Coton Bio Homme',
      description: 'T-shirt en coton biologique, disponible en plusieurs couleurs',
      price: 20,
      priceCDF: 50000,
      sku: 'VET-TSH-BIO-H',
      categoryId: 2,
      stock: 30,
      minStock: 15,
      image: '/images/products/tshirt-bio.jpg',
      barcode: '2345678901234',
      createdAt: '2024-01-12T16:20:00Z',
      updatedAt: '2024-01-12T16:20:00Z'
    },
    {
      id: 5,
      name: 'Jeans Femme Slim Fit',
      description: 'Jean slim fit en denim stretch, coupe moderne',
      price: 45,
      priceCDF: 112500,
      sku: 'VET-JEAN-SLIM-F',
      categoryId: 2,
      stock: 20,
      minStock: 8,
      image: '/images/products/jeans-femme.jpg',
      barcode: '3456789012345',
      createdAt: '2024-01-14T11:45:00Z',
      updatedAt: '2024-01-14T11:45:00Z'
    },

    // Alimentation
    {
      id: 6,
      name: 'Café Arabica Premium',
      description: 'Café en grains 100% Arabica, torréfaction artisanale',
      price: 12,
      priceCDF: 30000,
      sku: 'ALI-CAF-ARA-500',
      categoryId: 3,
      stock: 40,
      minStock: 15,
      image: '/images/products/cafe-arabica.jpg',
      barcode: '4567890123456',
      createdAt: '2024-01-09T08:30:00Z',
      updatedAt: '2024-01-09T08:30:00Z'
    },
    {
      id: 7,
      name: 'Chocolat Noir 70%',
      description: 'Tablette de chocolat noir 70% cacao, 100g',
      price: 5,
      priceCDF: 12500,
      sku: 'ALI-CHO-NOIR-100',
      categoryId: 3,
      stock: 60,
      minStock: 25,
      image: '/images/products/chocolat-noir.jpg',
      barcode: '5678901234567',
      createdAt: '2024-01-11T13:15:00Z',
      updatedAt: '2024-01-11T13:15:00Z'
    },

    // Cosmétiques
    {
      id: 8,
      name: 'Crème Hydratante Visage',
      description: 'Crème hydratante pour tous types de peau, 50ml',
      price: 30,
      priceCDF: 75000,
      sku: 'COS-CRE-HYD-50',
      categoryId: 4,
      stock: 35,
      minStock: 12,
      image: '/images/products/creme-hydratante.jpg',
      barcode: '6789012345678',
      createdAt: '2024-01-13T15:00:00Z',
      updatedAt: '2024-01-13T15:00:00Z'
    },
    {
      id: 9,
      name: 'Rouge à Lèvres Mat',
      description: 'Rouge à lèvres longue tenue, fini mat',
      price: 18,
      priceCDF: 45000,
      sku: 'COS-ROU-MAT-01',
      categoryId: 4,
      stock: 45,
      minStock: 20,
      image: '/images/products/rouge-levres.jpg',
      barcode: '7890123456789',
      createdAt: '2024-01-07T12:30:00Z',
      updatedAt: '2024-01-07T12:30:00Z'
    },

    // Maison
    {
      id: 10,
      name: 'Bougie Parfumée Vanille',
      description: 'Bougie parfumée à la vanille, durée 40h',
      price: 15,
      priceCDF: 37500,
      sku: 'MAI-BOU-VAN-40H',
      categoryId: 5,
      stock: 28,
      minStock: 10,
      image: '/images/products/bougie-vanille.jpg',
      barcode: '8901234567890',
      createdAt: '2024-01-06T17:45:00Z',
      updatedAt: '2024-01-06T17:45:00Z'
    }
  ];

  // Generate sales data for the last 30 days
  const sales = [];
  const today = new Date();
  
  for (let i = 0; i < 150; i++) {
    const saleDate = new Date(today);
    saleDate.setDate(saleDate.getDate() - Math.floor(Math.random() * 30));
    
    const product = products[Math.floor(Math.random() * products.length)];
    const quantity = Math.floor(Math.random() * 5) + 1;
    const unitPrice = product.price;
    const totalPrice = unitPrice * quantity;
    
    sales.push({
      id: i + 1,
      productId: product.id,
      productName: product.name,
      quantity,
      unitPrice,
      totalPrice,
      date: saleDate.toISOString(),
      customerName: generateCustomerName(),
      paymentMethod: ['Espèces', 'Carte', 'Mobile Money'][Math.floor(Math.random() * 3)],
      createdAt: saleDate.toISOString()
    });
  }

  return {
    products,
    sales,
    categories
  };
};

const generateCustomerName = () => {
  const firstNames = [
    'Jean', 'Marie', 'Pierre', 'Anne', 'Paul', 'Sophie', 'Michel', 'Catherine',
    'François', 'Isabelle', 'Jacques', 'Sylvie', 'André', 'Monique', 'Bernard',
    'Françoise', 'Philippe', 'Martine', 'Alain', 'Nicole', 'Claude', 'Brigitte'
  ];
  
  const lastNames = [
    'Martin', 'Bernard', 'Dubois', 'Thomas', 'Robert', 'Richard', 'Petit',
    'Durand', 'Leroy', 'Moreau', 'Simon', 'Laurent', 'Lefebvre', 'Michel',
    'Garcia', 'David', 'Bertrand', 'Roux', 'Vincent', 'Fournier', 'Morel'
  ];
  
  const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
  const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
  
  return `${firstName} ${lastName}`;
};
