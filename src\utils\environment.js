// Environment detection utilities

/**
 * Detect if the application is running in Electron environment
 * @returns {boolean} True if running in Electron, false if in browser
 */
export const isElectron = () => {
  // Check if we're in Electron environment
  if (typeof window !== 'undefined' && window.electronAPI) {
    return true;
  }
  
  // Check for Node.js environment (Electron main process)
  if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
    return true;
  }
  
  // Check for user agent (Electron renderer process)
  if (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.includes('Electron')) {
    return true;
  }
  
  return false;
};

/**
 * Detect if the application is running in a browser environment
 * @returns {boolean} True if running in browser, false otherwise
 */
export const isBrowser = () => {
  return typeof window !== 'undefined' && !isElectron();
};

/**
 * Detect if the application is running in Node.js environment
 * @returns {boolean} True if running in Node.js, false otherwise
 */
export const isNode = () => {
  return typeof process !== 'undefined' && process.versions && process.versions.node;
};

/**
 * Get the current environment type
 * @returns {'electron'|'browser'|'node'|'unknown'} The environment type
 */
export const getEnvironment = () => {
  if (isElectron()) return 'electron';
  if (isBrowser()) return 'browser';
  if (isNode()) return 'node';
  return 'unknown';
};

/**
 * Check if localStorage is available
 * @returns {boolean} True if localStorage is available
 */
export const hasLocalStorage = () => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false;
    }
    
    // Test if we can actually use localStorage
    const testKey = '__localStorage_test__';
    window.localStorage.setItem(testKey, 'test');
    window.localStorage.removeItem(testKey);
    return true;
  } catch (error) {
    console.warn('localStorage is not available:', error);
    return false;
  }
};

/**
 * Safe localStorage operations with fallbacks
 */
export const storage = {
  get: (key, defaultValue = null) => {
    try {
      if (!hasLocalStorage()) return defaultValue;
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.warn(`Error reading from localStorage for key "${key}":`, error);
      return defaultValue;
    }
  },
  
  set: (key, value) => {
    try {
      if (!hasLocalStorage()) {
        console.warn('localStorage not available, cannot save data');
        return false;
      }
      window.localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.warn(`Error writing to localStorage for key "${key}":`, error);
      return false;
    }
  },
  
  remove: (key) => {
    try {
      if (!hasLocalStorage()) return false;
      window.localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Error removing from localStorage for key "${key}":`, error);
      return false;
    }
  },
  
  clear: () => {
    try {
      if (!hasLocalStorage()) return false;
      window.localStorage.clear();
      return true;
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
      return false;
    }
  }
};

/**
 * Log environment information for debugging
 */
export const logEnvironmentInfo = () => {
  const env = getEnvironment();
  console.log('Environment Detection:', {
    environment: env,
    isElectron: isElectron(),
    isBrowser: isBrowser(),
    isNode: isNode(),
    hasLocalStorage: hasLocalStorage(),
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A',
    processVersions: typeof process !== 'undefined' ? process.versions : 'N/A'
  });
};
