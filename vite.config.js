import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'build',
    assetsDir: 'assets',
    rollupOptions: {
      external: [
        'better-sqlite3',
        'electron',
        'fs',
        'path',
        'os'
      ]
    }
  },
  server: {
    port: 5173
  },
  define: {
    __IS_ELECTRON__: 'false'
  },
  optimizeDeps: {
    exclude: ['better-sqlite3']
  }
})
