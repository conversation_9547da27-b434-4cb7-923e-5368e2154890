import { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import Sidebar from './components/Layout/Sidebar';
import TopBar from './components/Layout/TopBar';
import Dashboard from './components/Dashboard/Dashboard';
import Products from './components/Products/Products';
import Sales from './components/Sales/Sales';
import Reports from './components/Reports/Reports';
import Analytics from './components/Analytics/Analytics';
import Users from './components/Users/<USER>';
import Login from './components/Auth/Login';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DataProvider } from './contexts/DataContext';
import { initializeDatabase } from './data/database';

const DRAWER_WIDTH = 280;

function AppContent() {
  const { user, isAuthenticated, loading } = useAuth();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [appInitialized, setAppInitialized] = useState(false);

  useEffect(() => {
    // Initialize database on app start with timeout
    const initDB = async () => {
      try {
        console.log('Initializing database...');

        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Database initialization timeout')), 3000)
        );

        await Promise.race([
          initializeDatabase(),
          timeoutPromise
        ]);

        console.log('Database initialization completed');
      } catch (error) {
        console.error('Database initialization failed:', error);
        // Continue anyway - the app should still work with mock data
      } finally {
        // Mark app as initialized regardless of database status
        setAppInitialized(true);
        console.log('App initialization completed');
      }
    };

    initDB();
  }, []);

  // Show loading while auth is being checked or app is initializing
  if (loading || !appInitialized) {
    console.log('App loading...', { authLoading: loading, appInitialized });
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          backgroundColor: '#f5f5f5'
        }}
      >
        <Typography variant="h6" sx={{ mb: 2 }}>
          Chargement de l'application...
        </Typography>
        <Box sx={{ width: '200px', height: '4px', backgroundColor: '#e0e0e0', borderRadius: '2px' }}>
          <Box
            sx={{
              width: '100%',
              height: '100%',
              backgroundColor: '#1976d2',
              borderRadius: '2px',
              animation: 'pulse 1.5s ease-in-out infinite'
            }}
          />
        </Box>
      </Box>
    );
  }

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  if (!isAuthenticated) {
    return <Login />;
  }

  return (
    <Box sx={{ display: 'flex' }}>
      <TopBar 
        drawerWidth={DRAWER_WIDTH}
        onMenuClick={handleDrawerToggle}
        user={user}
      />
      <Sidebar 
        drawerWidth={DRAWER_WIDTH}
        mobileOpen={mobileOpen}
        onDrawerToggle={handleDrawerToggle}
        userRole={user?.role}
      />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          mt: 8,
          minHeight: 'calc(100vh - 64px)',
          backgroundColor: '#f5f5f5'
        }}
      >
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/products" element={<Products />} />
          <Route path="/sales" element={<Sales />} />
          {/* Reports only for super admin */}
          {user?.role === 'superadmin' && (
            <Route path="/reports" element={<Reports />} />
          )}
          {/* Analytics only for super admin */}
          {user?.role === 'superadmin' && (
            <Route path="/analytics" element={<Analytics />} />
          )}
          {/* Users management for admin and super admin */}
          {(user?.role === 'admin' || user?.role === 'superadmin') && (
            <Route path="/users" element={<Users />} />
          )}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </Box>
    </Box>
  );
}

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <AppContent />
      </DataProvider>
    </AuthProvider>
  );
}

export default App;
