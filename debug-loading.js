// Debug script to help identify loading issues
// Run this in the browser console to get detailed information

console.log('=== MABOUTIQUE LOADING DEBUG ===');

// 1. Check environment
console.log('1. Environment Check:');
console.log('- Window object:', typeof window);
console.log('- Document ready state:', document.readyState);
console.log('- User agent:', navigator.userAgent);

// 2. Check for React
console.log('\n2. React Check:');
console.log('- React available:', typeof React !== 'undefined');
console.log('- ReactDOM available:', typeof ReactDOM !== 'undefined');

// 3. Check DOM elements
console.log('\n3. DOM Elements:');
const loadingElement = document.getElementById('loading');
const rootElement = document.getElementById('root');
console.log('- Loading element:', loadingElement ? 'Found' : 'Not found');
console.log('- Root element:', rootElement ? 'Found' : 'Not found');

if (loadingElement) {
  console.log('- Loading element display:', getComputedStyle(loadingElement).display);
  console.log('- Loading element opacity:', getComputedStyle(loadingElement).opacity);
}

if (rootElement) {
  console.log('- Root element children:', rootElement.children.length);
  console.log('- Root element innerHTML length:', rootElement.innerHTML.length);
}

// 4. Check localStorage
console.log('\n4. LocalStorage Check:');
try {
  const testKey = '__debug_test__';
  localStorage.setItem(testKey, 'test');
  const testValue = localStorage.getItem(testKey);
  localStorage.removeItem(testKey);
  console.log('- LocalStorage working:', testValue === 'test');
  
  // Check for app data
  const userData = localStorage.getItem('maboutique_user');
  const productsData = localStorage.getItem('maboutique_products');
  console.log('- User data exists:', !!userData);
  console.log('- Products data exists:', !!productsData);
} catch (error) {
  console.log('- LocalStorage error:', error.message);
}

// 5. Check for errors
console.log('\n5. Error Check:');
const errors = [];
const originalError = console.error;
console.error = function(...args) {
  errors.push(args.join(' '));
  originalError.apply(console, args);
};

// 6. Network requests
console.log('\n6. Network Check:');
console.log('- Current URL:', window.location.href);
console.log('- Origin:', window.location.origin);

// 7. Module loading
console.log('\n7. Module Loading:');
console.log('- ES6 modules supported:', typeof import === 'function');

// 8. Check for specific errors
setTimeout(() => {
  console.log('\n8. Collected Errors (after 2 seconds):');
  if (errors.length === 0) {
    console.log('- No errors detected');
  } else {
    errors.forEach((error, index) => {
      console.log(`- Error ${index + 1}:`, error);
    });
  }
  
  // Final status
  console.log('\n=== FINAL STATUS ===');
  const loadingStillVisible = loadingElement && getComputedStyle(loadingElement).display !== 'none';
  const appLoaded = rootElement && rootElement.children.length > 0;
  
  console.log('- Loading screen visible:', loadingStillVisible);
  console.log('- App content loaded:', appLoaded);
  
  if (loadingStillVisible && !appLoaded) {
    console.log('🔴 ISSUE: Loading screen stuck - app not loaded');
  } else if (!loadingStillVisible && appLoaded) {
    console.log('✅ SUCCESS: App loaded correctly');
  } else if (loadingStillVisible && appLoaded) {
    console.log('⚠️ WARNING: App loaded but loading screen still visible');
  } else {
    console.log('❓ UNKNOWN: Unexpected state');
  }
}, 2000);

// 9. Manual loading screen hide function
window.debugHideLoading = function() {
  const loading = document.getElementById('loading');
  if (loading) {
    loading.style.opacity = '0';
    loading.style.transition = 'opacity 0.5s ease-out';
    setTimeout(() => {
      loading.style.display = 'none';
      console.log('✅ Loading screen manually hidden');
    }, 500);
  } else {
    console.log('❌ Loading element not found');
  }
};

console.log('\n💡 TIP: If loading is stuck, run debugHideLoading() to manually hide it');
console.log('=== END DEBUG ===');
