// Simple script to check if the web app is loading properly
const puppeteer = require('puppeteer');

async function checkWebApp() {
  let browser;
  try {
    console.log('Starting browser...');
    browser = await puppeteer.launch({ 
      headless: false, 
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      console.log(`BROWSER: ${msg.type()}: ${msg.text()}`);
    });
    
    // Listen for errors
    page.on('error', err => {
      console.error('BROWSER ERROR:', err);
    });
    
    page.on('pageerror', err => {
      console.error('PAGE ERROR:', err);
    });
    
    console.log('Navigating to web app...');
    await page.goto('http://localhost:5179/', { 
      waitUntil: 'networkidle0',
      timeout: 10000 
    });
    
    console.log('Waiting for loading screen to disappear...');
    
    // Wait for loading screen to disappear
    try {
      await page.waitForFunction(
        () => {
          const loading = document.getElementById('loading');
          return !loading || loading.style.display === 'none' || loading.style.opacity === '0';
        },
        { timeout: 10000 }
      );
      console.log('✅ Loading screen disappeared successfully!');
    } catch (error) {
      console.log('❌ Loading screen did not disappear within timeout');
      
      // Check if loading screen is still visible
      const loadingVisible = await page.evaluate(() => {
        const loading = document.getElementById('loading');
        return loading && loading.style.display !== 'none';
      });
      
      console.log('Loading screen still visible:', loadingVisible);
    }
    
    // Check if React app is mounted
    const reactMounted = await page.evaluate(() => {
      const root = document.getElementById('root');
      return root && root.children.length > 0;
    });
    
    console.log('React app mounted:', reactMounted);
    
    // Take a screenshot
    await page.screenshot({ path: 'web-app-screenshot.png', fullPage: true });
    console.log('Screenshot saved as web-app-screenshot.png');
    
    // Wait a bit more to see the app
    await page.waitForTimeout(5000);
    
  } catch (error) {
    console.error('Error checking web app:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is available
try {
  checkWebApp();
} catch (error) {
  console.log('Puppeteer not available, skipping automated check');
  console.log('Please manually check http://localhost:5179/');
}
