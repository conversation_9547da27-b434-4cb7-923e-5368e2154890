<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Maboutique - Système de Gestion</title>
    <style>
      body {
        margin: 0;
        font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
        background-color: #f5f5f5;
      }
      
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
        margin-bottom: 20px;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .loading-text {
        font-size: 18px;
        font-weight: 300;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div id="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">
        <h2>Ma boutique</h2>
        <p>Chargement du système de gestion...</p>
      </div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
