<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Maboutique App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-top: 20px;
        }
        .test-results {
            margin-top: 20px;
        }
        .credentials {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Maboutique Application</h1>
        
        <div class="credentials">
            <h3>Identifiants de test:</h3>
            <p><strong>Super Admin:</strong> superadmin / super123</p>
            <p><strong>Admin:</strong> admin / admin123</p>
            <p><strong>Employé:</strong> employe / emp123</p>
        </div>

        <div>
            <button onclick="loadApp()">Charger l'Application</button>
            <button onclick="testLogin()">Tester la Connexion</button>
            <button onclick="checkStatus()">Vérifier le Statut</button>
            <button onclick="clearFrame()">Effacer</button>
        </div>

        <div id="status" class="status info">
            Prêt à tester l'application
        </div>

        <div class="test-results">
            <h3>Résultats des Tests:</h3>
            <div id="test-results"></div>
        </div>

        <div id="app-frame"></div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
        }

        function addTestResult(test, result, details = '') {
            const results = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${result ? 'success' : 'error'}`;
            resultDiv.innerHTML = `<strong>${test}:</strong> ${result ? 'RÉUSSI' : 'ÉCHEC'} ${details}`;
            results.appendChild(resultDiv);
        }

        function loadApp() {
            updateStatus('Chargement de l\'application...', 'info');
            
            const frameContainer = document.getElementById('app-frame');
            frameContainer.innerHTML = '<iframe id="app-iframe" src="http://localhost:5179/"></iframe>';
            
            const iframe = document.getElementById('app-iframe');
            
            iframe.onload = function() {
                updateStatus('Application chargée', 'success');
                
                // Test if loading screen disappears
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const loadingElement = iframeDoc.getElementById('loading');
                        
                        if (!loadingElement) {
                            addTestResult('Écran de chargement', false, '- Élément loading non trouvé');
                        } else if (loadingElement.style.display === 'none' || loadingElement.style.opacity === '0') {
                            addTestResult('Écran de chargement', true, '- Correctement masqué');
                        } else {
                            addTestResult('Écran de chargement', false, '- Toujours visible');
                        }
                        
                        // Check if React app is mounted
                        const root = iframeDoc.getElementById('root');
                        const reactMounted = root && root.children.length > 0;
                        addTestResult('Application React', reactMounted, reactMounted ? '- Montée correctement' : '- Non montée');
                        
                        // Check for login form
                        const loginForm = iframeDoc.querySelector('form') || iframeDoc.querySelector('[type="password"]');
                        addTestResult('Interface de connexion', !!loginForm, loginForm ? '- Formulaire trouvé' : '- Formulaire non trouvé');
                        
                    } catch (error) {
                        addTestResult('Accès iframe', false, `- Erreur: ${error.message}`);
                    }
                }, 3000);
            };
            
            iframe.onerror = function() {
                updateStatus('Erreur lors du chargement de l\'application', 'error');
                addTestResult('Chargement application', false, '- Erreur de chargement');
            };
        }

        function testLogin() {
            updateStatus('Test de connexion en cours...', 'info');
            
            const iframe = document.getElementById('app-iframe');
            if (!iframe) {
                updateStatus('Veuillez d\'abord charger l\'application', 'warning');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Try to find login inputs
                const usernameInput = iframeDoc.querySelector('input[type="text"]') || 
                                    iframeDoc.querySelector('input[name*="username"]') ||
                                    iframeDoc.querySelector('input[name*="user"]');
                                    
                const passwordInput = iframeDoc.querySelector('input[type="password"]');
                
                if (usernameInput && passwordInput) {
                    // Fill in super admin credentials
                    usernameInput.value = 'superadmin';
                    passwordInput.value = 'super123';
                    
                    // Trigger input events
                    usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
                    passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // Try to find and click login button
                    const loginButton = iframeDoc.querySelector('button[type="submit"]') ||
                                      iframeDoc.querySelector('button:contains("Connexion")') ||
                                      iframeDoc.querySelector('button');
                    
                    if (loginButton) {
                        loginButton.click();
                        updateStatus('Tentative de connexion...', 'info');
                        
                        // Check if login was successful after a delay
                        setTimeout(() => {
                            try {
                                const dashboard = iframeDoc.querySelector('h1') || 
                                                iframeDoc.querySelector('[role="main"]');
                                if (dashboard) {
                                    addTestResult('Connexion Super Admin', true, '- Accès au tableau de bord');
                                    updateStatus('Connexion réussie!', 'success');
                                } else {
                                    addTestResult('Connexion Super Admin', false, '- Pas d\'accès au tableau de bord');
                                }
                            } catch (error) {
                                addTestResult('Vérification connexion', false, `- Erreur: ${error.message}`);
                            }
                        }, 2000);
                    } else {
                        addTestResult('Bouton de connexion', false, '- Bouton non trouvé');
                    }
                } else {
                    addTestResult('Champs de connexion', false, '- Champs non trouvés');
                }
            } catch (error) {
                updateStatus('Erreur lors du test de connexion', 'error');
                addTestResult('Test de connexion', false, `- Erreur: ${error.message}`);
            }
        }

        function checkStatus() {
            const iframe = document.getElementById('app-iframe');
            if (!iframe) {
                updateStatus('Aucune application chargée', 'warning');
                return;
            }
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const title = iframeDoc.title;
                const url = iframe.src;
                
                updateStatus(`Application active - Titre: ${title}`, 'success');
                addTestResult('Statut application', true, `- URL: ${url}, Titre: ${title}`);
            } catch (error) {
                updateStatus('Impossible d\'accéder à l\'application', 'error');
                addTestResult('Accès application', false, `- Erreur: ${error.message}`);
            }
        }

        function clearFrame() {
            document.getElementById('app-frame').innerHTML = '';
            document.getElementById('test-results').innerHTML = '';
            updateStatus('Prêt à tester l\'application', 'info');
        }

        // Auto-load app on page load
        window.onload = function() {
            setTimeout(loadApp, 1000);
        };
    </script>
</body>
</html>
